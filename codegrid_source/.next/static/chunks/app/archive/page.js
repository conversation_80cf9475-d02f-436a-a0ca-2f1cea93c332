/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/archive/page"],{

/***/ "(app-pages-browser)/./node_modules/lenis/dist/lenis-react.mjs":
/*!*************************************************!*\
  !*** ./node_modules/lenis/dist/lenis-react.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Lenis: () => (/* binding */ ReactLenis),\n/* harmony export */   LenisContext: () => (/* binding */ LenisContext),\n/* harmony export */   ReactLenis: () => (/* binding */ ReactLenis),\n/* harmony export */   \"default\": () => (/* binding */ ReactLenis),\n/* harmony export */   useLenis: () => (/* binding */ useLenis)\n/* harmony export */ });\n/* harmony import */ var lenis__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lenis */ \"(app-pages-browser)/./node_modules/lenis/dist/lenis.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Lenis,LenisContext,ReactLenis,default,useLenis auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n// packages/react/src/provider.tsx\n\n\n// packages/react/src/store.ts\n\nvar Store = class {\n    set(state) {\n        this.state = state;\n        for (let listener of this.listeners){\n            listener(this.state);\n        }\n    }\n    subscribe(listener) {\n        this.listeners = [\n            ...this.listeners,\n            listener\n        ];\n        return ()=>{\n            this.listeners = this.listeners.filter((l)=>l !== listener);\n        };\n    }\n    get() {\n        return this.state;\n    }\n    constructor(state){\n        this.listeners = [];\n        this.state = state;\n    }\n};\nfunction useStore(store) {\n    _s();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(store.get());\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useStore.useEffect\": ()=>{\n            return store.subscribe({\n                \"useStore.useEffect\": (state2)=>setState(state2)\n            }[\"useStore.useEffect\"]);\n        }\n    }[\"useStore.useEffect\"], [\n        store\n    ]);\n    return state;\n}\n_s(useStore, \"27Lm6MbSoJ0zrfq7rZXMlG5Mf2g=\");\n// packages/react/src/provider.tsx\n\nvar LenisContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\nvar rootLenisContextStore = new Store(null);\nvar ReactLenis = /*#__PURE__*/ _s1((0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(_c = _s1((param, ref)=>{\n    let { children, root = false, options = {}, className, autoRaf = true, style, props } = param;\n    _s1();\n    const wrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [lenis, setLenis] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, {\n        \"ReactLenis.useImperativeHandle\": ()=>({\n                wrapper: wrapperRef.current,\n                content: contentRef.current,\n                lenis\n            })\n    }[\"ReactLenis.useImperativeHandle\"], [\n        lenis\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"ReactLenis.useEffect2\": ()=>{\n            var _options_autoRaf;\n            const lenis2 = new lenis__WEBPACK_IMPORTED_MODULE_2__[\"default\"]({\n                ...options,\n                ...!root && {\n                    wrapper: wrapperRef.current,\n                    content: contentRef.current\n                },\n                autoRaf: (_options_autoRaf = options === null || options === void 0 ? void 0 : options.autoRaf) !== null && _options_autoRaf !== void 0 ? _options_autoRaf : autoRaf\n            });\n            setLenis(lenis2);\n            return ({\n                \"ReactLenis.useEffect2\": ()=>{\n                    lenis2.destroy();\n                    setLenis(void 0);\n                }\n            })[\"ReactLenis.useEffect2\"];\n        }\n    }[\"ReactLenis.useEffect2\"], [\n        root,\n        JSON.stringify(options)\n    ]);\n    const callbacksRefs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const addCallback = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"ReactLenis.useCallback[addCallback]\": (callback, priority)=>{\n            callbacksRefs.current.push({\n                callback,\n                priority\n            });\n            callbacksRefs.current.sort({\n                \"ReactLenis.useCallback[addCallback]\": (a, b)=>a.priority - b.priority\n            }[\"ReactLenis.useCallback[addCallback]\"]);\n        }\n    }[\"ReactLenis.useCallback[addCallback]\"], []);\n    const removeCallback = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"ReactLenis.useCallback[removeCallback]\": (callback)=>{\n            callbacksRefs.current = callbacksRefs.current.filter({\n                \"ReactLenis.useCallback[removeCallback]\": (cb)=>cb.callback !== callback\n            }[\"ReactLenis.useCallback[removeCallback]\"]);\n        }\n    }[\"ReactLenis.useCallback[removeCallback]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"ReactLenis.useEffect2\": ()=>{\n            if (root && lenis) {\n                rootLenisContextStore.set({\n                    lenis,\n                    addCallback,\n                    removeCallback\n                });\n                return ({\n                    \"ReactLenis.useEffect2\": ()=>rootLenisContextStore.set(null)\n                })[\"ReactLenis.useEffect2\"];\n            }\n        }\n    }[\"ReactLenis.useEffect2\"], [\n        root,\n        lenis,\n        addCallback,\n        removeCallback\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"ReactLenis.useEffect2\": ()=>{\n            if (!lenis) return;\n            const onScroll = {\n                \"ReactLenis.useEffect2.onScroll\": (data)=>{\n                    for(let i = 0; i < callbacksRefs.current.length; i++){\n                        var _callbacksRefs_current_i;\n                        (_callbacksRefs_current_i = callbacksRefs.current[i]) === null || _callbacksRefs_current_i === void 0 ? void 0 : _callbacksRefs_current_i.callback(data);\n                    }\n                }\n            }[\"ReactLenis.useEffect2.onScroll\"];\n            lenis.on(\"scroll\", onScroll);\n            return ({\n                \"ReactLenis.useEffect2\": ()=>{\n                    lenis.off(\"scroll\", onScroll);\n                }\n            })[\"ReactLenis.useEffect2\"];\n        }\n    }[\"ReactLenis.useEffect2\"], [\n        lenis\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(LenisContext.Provider, {\n        value: {\n            lenis,\n            addCallback,\n            removeCallback\n        },\n        children: root ? children : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n            ref: wrapperRef,\n            className,\n            style,\n            ...props,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n                ref: contentRef,\n                children\n            })\n        })\n    });\n}, \"xcSApmkuWJ3ZqV8ukURsVWK4USY=\", false, function() {\n    return [\n        react__WEBPACK_IMPORTED_MODULE_0__.useState,\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect,\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect,\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect\n    ];\n})), \"xcSApmkuWJ3ZqV8ukURsVWK4USY=\", false, function() {\n    return [\n        react__WEBPACK_IMPORTED_MODULE_0__.useState,\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect,\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect,\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect\n    ];\n});\n_c1 = ReactLenis;\n// packages/react/src/use-lenis.ts\n\nvar fallbackContext = {};\nfunction useLenis(callback) {\n    let deps = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [], priority = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n    _s2();\n    const localContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(LenisContext);\n    const rootContext = useStore(rootLenisContextStore);\n    var _ref;\n    const currentContext = (_ref = localContext !== null && localContext !== void 0 ? localContext : rootContext) !== null && _ref !== void 0 ? _ref : fallbackContext;\n    const { lenis, addCallback, removeCallback } = currentContext;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useLenis.useEffect3\": ()=>{\n            if (!callback || !addCallback || !removeCallback || !lenis) return;\n            addCallback(callback, priority);\n            callback(lenis);\n            return ({\n                \"useLenis.useEffect3\": ()=>{\n                    removeCallback(callback);\n                }\n            })[\"useLenis.useEffect3\"];\n        }\n    }[\"useLenis.useEffect3\"], [\n        lenis,\n        addCallback,\n        removeCallback,\n        priority,\n        ...deps\n    ]);\n    return lenis;\n}\n_s2(useLenis, \"+TLA5bEQJrXkJq7S4o3Xtv63QKc=\", false, function() {\n    return [\n        useStore,\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect\n    ];\n});\n //# sourceMappingURL=lenis-react.mjs.map\nvar _c, _c1;\n$RefreshReg$(_c, \"ReactLenis$forwardRef\");\n$RefreshReg$(_c1, \"ReactLenis\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lenis/dist/lenis-react.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lenis/dist/lenis.mjs":
/*!*******************************************!*\
  !*** ./node_modules/lenis/dist/lenis.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Lenis)\n/* harmony export */ });\n// package.json\nvar version = \"1.3.8\";\n\n// packages/core/src/maths.ts\nfunction clamp(min, input, max) {\n  return Math.max(min, Math.min(input, max));\n}\nfunction lerp(x, y, t) {\n  return (1 - t) * x + t * y;\n}\nfunction damp(x, y, lambda, deltaTime) {\n  return lerp(x, y, 1 - Math.exp(-lambda * deltaTime));\n}\nfunction modulo(n, d) {\n  return (n % d + d) % d;\n}\n\n// packages/core/src/animate.ts\nvar Animate = class {\n  isRunning = false;\n  value = 0;\n  from = 0;\n  to = 0;\n  currentTime = 0;\n  // These are instanciated in the fromTo method\n  lerp;\n  duration;\n  easing;\n  onUpdate;\n  /**\n   * Advance the animation by the given delta time\n   *\n   * @param deltaTime - The time in seconds to advance the animation\n   */\n  advance(deltaTime) {\n    if (!this.isRunning) return;\n    let completed = false;\n    if (this.duration && this.easing) {\n      this.currentTime += deltaTime;\n      const linearProgress = clamp(0, this.currentTime / this.duration, 1);\n      completed = linearProgress >= 1;\n      const easedProgress = completed ? 1 : this.easing(linearProgress);\n      this.value = this.from + (this.to - this.from) * easedProgress;\n    } else if (this.lerp) {\n      this.value = damp(this.value, this.to, this.lerp * 60, deltaTime);\n      if (Math.round(this.value) === this.to) {\n        this.value = this.to;\n        completed = true;\n      }\n    } else {\n      this.value = this.to;\n      completed = true;\n    }\n    if (completed) {\n      this.stop();\n    }\n    this.onUpdate?.(this.value, completed);\n  }\n  /** Stop the animation */\n  stop() {\n    this.isRunning = false;\n  }\n  /**\n   * Set up the animation from a starting value to an ending value\n   * with optional parameters for lerping, duration, easing, and onUpdate callback\n   *\n   * @param from - The starting value\n   * @param to - The ending value\n   * @param options - Options for the animation\n   */\n  fromTo(from, to, { lerp: lerp2, duration, easing, onStart, onUpdate }) {\n    this.from = this.value = from;\n    this.to = to;\n    this.lerp = lerp2;\n    this.duration = duration;\n    this.easing = easing;\n    this.currentTime = 0;\n    this.isRunning = true;\n    onStart?.();\n    this.onUpdate = onUpdate;\n  }\n};\n\n// packages/core/src/debounce.ts\nfunction debounce(callback, delay) {\n  let timer;\n  return function(...args) {\n    let context = this;\n    clearTimeout(timer);\n    timer = setTimeout(() => {\n      timer = void 0;\n      callback.apply(context, args);\n    }, delay);\n  };\n}\n\n// packages/core/src/dimensions.ts\nvar Dimensions = class {\n  constructor(wrapper, content, { autoResize = true, debounce: debounceValue = 250 } = {}) {\n    this.wrapper = wrapper;\n    this.content = content;\n    if (autoResize) {\n      this.debouncedResize = debounce(this.resize, debounceValue);\n      if (this.wrapper instanceof Window) {\n        window.addEventListener(\"resize\", this.debouncedResize, false);\n      } else {\n        this.wrapperResizeObserver = new ResizeObserver(this.debouncedResize);\n        this.wrapperResizeObserver.observe(this.wrapper);\n      }\n      this.contentResizeObserver = new ResizeObserver(this.debouncedResize);\n      this.contentResizeObserver.observe(this.content);\n    }\n    this.resize();\n  }\n  width = 0;\n  height = 0;\n  scrollHeight = 0;\n  scrollWidth = 0;\n  // These are instanciated in the constructor as they need information from the options\n  debouncedResize;\n  wrapperResizeObserver;\n  contentResizeObserver;\n  destroy() {\n    this.wrapperResizeObserver?.disconnect();\n    this.contentResizeObserver?.disconnect();\n    if (this.wrapper === window && this.debouncedResize) {\n      window.removeEventListener(\"resize\", this.debouncedResize, false);\n    }\n  }\n  resize = () => {\n    this.onWrapperResize();\n    this.onContentResize();\n  };\n  onWrapperResize = () => {\n    if (this.wrapper instanceof Window) {\n      this.width = window.innerWidth;\n      this.height = window.innerHeight;\n    } else {\n      this.width = this.wrapper.clientWidth;\n      this.height = this.wrapper.clientHeight;\n    }\n  };\n  onContentResize = () => {\n    if (this.wrapper instanceof Window) {\n      this.scrollHeight = this.content.scrollHeight;\n      this.scrollWidth = this.content.scrollWidth;\n    } else {\n      this.scrollHeight = this.wrapper.scrollHeight;\n      this.scrollWidth = this.wrapper.scrollWidth;\n    }\n  };\n  get limit() {\n    return {\n      x: this.scrollWidth - this.width,\n      y: this.scrollHeight - this.height\n    };\n  }\n};\n\n// packages/core/src/emitter.ts\nvar Emitter = class {\n  events = {};\n  /**\n   * Emit an event with the given data\n   * @param event Event name\n   * @param args Data to pass to the event handlers\n   */\n  emit(event, ...args) {\n    let callbacks = this.events[event] || [];\n    for (let i = 0, length = callbacks.length; i < length; i++) {\n      callbacks[i]?.(...args);\n    }\n  }\n  /**\n   * Add a callback to the event\n   * @param event Event name\n   * @param cb Callback function\n   * @returns Unsubscribe function\n   */\n  on(event, cb) {\n    this.events[event]?.push(cb) || (this.events[event] = [cb]);\n    return () => {\n      this.events[event] = this.events[event]?.filter((i) => cb !== i);\n    };\n  }\n  /**\n   * Remove a callback from the event\n   * @param event Event name\n   * @param callback Callback function\n   */\n  off(event, callback) {\n    this.events[event] = this.events[event]?.filter((i) => callback !== i);\n  }\n  /**\n   * Remove all event listeners and clean up\n   */\n  destroy() {\n    this.events = {};\n  }\n};\n\n// packages/core/src/virtual-scroll.ts\nvar LINE_HEIGHT = 100 / 6;\nvar listenerOptions = { passive: false };\nvar VirtualScroll = class {\n  constructor(element, options = { wheelMultiplier: 1, touchMultiplier: 1 }) {\n    this.element = element;\n    this.options = options;\n    window.addEventListener(\"resize\", this.onWindowResize, false);\n    this.onWindowResize();\n    this.element.addEventListener(\"wheel\", this.onWheel, listenerOptions);\n    this.element.addEventListener(\n      \"touchstart\",\n      this.onTouchStart,\n      listenerOptions\n    );\n    this.element.addEventListener(\n      \"touchmove\",\n      this.onTouchMove,\n      listenerOptions\n    );\n    this.element.addEventListener(\"touchend\", this.onTouchEnd, listenerOptions);\n  }\n  touchStart = {\n    x: 0,\n    y: 0\n  };\n  lastDelta = {\n    x: 0,\n    y: 0\n  };\n  window = {\n    width: 0,\n    height: 0\n  };\n  emitter = new Emitter();\n  /**\n   * Add an event listener for the given event and callback\n   *\n   * @param event Event name\n   * @param callback Callback function\n   */\n  on(event, callback) {\n    return this.emitter.on(event, callback);\n  }\n  /** Remove all event listeners and clean up */\n  destroy() {\n    this.emitter.destroy();\n    window.removeEventListener(\"resize\", this.onWindowResize, false);\n    this.element.removeEventListener(\"wheel\", this.onWheel, listenerOptions);\n    this.element.removeEventListener(\n      \"touchstart\",\n      this.onTouchStart,\n      listenerOptions\n    );\n    this.element.removeEventListener(\n      \"touchmove\",\n      this.onTouchMove,\n      listenerOptions\n    );\n    this.element.removeEventListener(\n      \"touchend\",\n      this.onTouchEnd,\n      listenerOptions\n    );\n  }\n  /**\n   * Event handler for 'touchstart' event\n   *\n   * @param event Touch event\n   */\n  onTouchStart = (event) => {\n    const { clientX, clientY } = event.targetTouches ? event.targetTouches[0] : event;\n    this.touchStart.x = clientX;\n    this.touchStart.y = clientY;\n    this.lastDelta = {\n      x: 0,\n      y: 0\n    };\n    this.emitter.emit(\"scroll\", {\n      deltaX: 0,\n      deltaY: 0,\n      event\n    });\n  };\n  /** Event handler for 'touchmove' event */\n  onTouchMove = (event) => {\n    const { clientX, clientY } = event.targetTouches ? event.targetTouches[0] : event;\n    const deltaX = -(clientX - this.touchStart.x) * this.options.touchMultiplier;\n    const deltaY = -(clientY - this.touchStart.y) * this.options.touchMultiplier;\n    this.touchStart.x = clientX;\n    this.touchStart.y = clientY;\n    this.lastDelta = {\n      x: deltaX,\n      y: deltaY\n    };\n    this.emitter.emit(\"scroll\", {\n      deltaX,\n      deltaY,\n      event\n    });\n  };\n  onTouchEnd = (event) => {\n    this.emitter.emit(\"scroll\", {\n      deltaX: this.lastDelta.x,\n      deltaY: this.lastDelta.y,\n      event\n    });\n  };\n  /** Event handler for 'wheel' event */\n  onWheel = (event) => {\n    let { deltaX, deltaY, deltaMode } = event;\n    const multiplierX = deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.window.width : 1;\n    const multiplierY = deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.window.height : 1;\n    deltaX *= multiplierX;\n    deltaY *= multiplierY;\n    deltaX *= this.options.wheelMultiplier;\n    deltaY *= this.options.wheelMultiplier;\n    this.emitter.emit(\"scroll\", { deltaX, deltaY, event });\n  };\n  onWindowResize = () => {\n    this.window = {\n      width: window.innerWidth,\n      height: window.innerHeight\n    };\n  };\n};\n\n// packages/core/src/lenis.ts\nvar defaultEasing = (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t));\nvar Lenis = class {\n  _isScrolling = false;\n  // true when scroll is animating\n  _isStopped = false;\n  // true if user should not be able to scroll - enable/disable programmatically\n  _isLocked = false;\n  // same as isStopped but enabled/disabled when scroll reaches target\n  _preventNextNativeScrollEvent = false;\n  _resetVelocityTimeout = null;\n  __rafID = null;\n  /**\n   * Whether or not the user is touching the screen\n   */\n  isTouching;\n  /**\n   * The time in ms since the lenis instance was created\n   */\n  time = 0;\n  /**\n   * User data that will be forwarded through the scroll event\n   *\n   * @example\n   * lenis.scrollTo(100, {\n   *   userData: {\n   *     foo: 'bar'\n   *   }\n   * })\n   */\n  userData = {};\n  /**\n   * The last velocity of the scroll\n   */\n  lastVelocity = 0;\n  /**\n   * The current velocity of the scroll\n   */\n  velocity = 0;\n  /**\n   * The direction of the scroll\n   */\n  direction = 0;\n  /**\n   * The options passed to the lenis instance\n   */\n  options;\n  /**\n   * The target scroll value\n   */\n  targetScroll;\n  /**\n   * The animated scroll value\n   */\n  animatedScroll;\n  // These are instanciated here as they don't need information from the options\n  animate = new Animate();\n  emitter = new Emitter();\n  // These are instanciated in the constructor as they need information from the options\n  dimensions;\n  // This is not private because it's used in the Snap class\n  virtualScroll;\n  constructor({\n    wrapper = window,\n    content = document.documentElement,\n    eventsTarget = wrapper,\n    smoothWheel = true,\n    syncTouch = false,\n    syncTouchLerp = 0.075,\n    touchInertiaExponent = 1.7,\n    duration,\n    // in seconds\n    easing,\n    lerp: lerp2 = 0.1,\n    infinite = false,\n    orientation = \"vertical\",\n    // vertical, horizontal\n    gestureOrientation = \"vertical\",\n    // vertical, horizontal, both\n    touchMultiplier = 1,\n    wheelMultiplier = 1,\n    autoResize = true,\n    prevent,\n    virtualScroll,\n    overscroll = true,\n    autoRaf = false,\n    anchors = false,\n    autoToggle = false,\n    // https://caniuse.com/?search=transition-behavior\n    allowNestedScroll = false,\n    __experimental__naiveDimensions = false\n  } = {}) {\n    window.lenisVersion = version;\n    if (!wrapper || wrapper === document.documentElement) {\n      wrapper = window;\n    }\n    if (typeof duration === \"number\" && typeof easing !== \"function\") {\n      easing = defaultEasing;\n    } else if (typeof easing === \"function\" && typeof duration !== \"number\") {\n      duration = 1;\n    }\n    this.options = {\n      wrapper,\n      content,\n      eventsTarget,\n      smoothWheel,\n      syncTouch,\n      syncTouchLerp,\n      touchInertiaExponent,\n      duration,\n      easing,\n      lerp: lerp2,\n      infinite,\n      gestureOrientation,\n      orientation,\n      touchMultiplier,\n      wheelMultiplier,\n      autoResize,\n      prevent,\n      virtualScroll,\n      overscroll,\n      autoRaf,\n      anchors,\n      autoToggle,\n      allowNestedScroll,\n      __experimental__naiveDimensions\n    };\n    this.dimensions = new Dimensions(wrapper, content, { autoResize });\n    this.updateClassName();\n    this.targetScroll = this.animatedScroll = this.actualScroll;\n    this.options.wrapper.addEventListener(\"scroll\", this.onNativeScroll, false);\n    this.options.wrapper.addEventListener(\"scrollend\", this.onScrollEnd, {\n      capture: true\n    });\n    if (this.options.anchors && this.options.wrapper === window) {\n      this.options.wrapper.addEventListener(\n        \"click\",\n        this.onClick,\n        false\n      );\n    }\n    this.options.wrapper.addEventListener(\n      \"pointerdown\",\n      this.onPointerDown,\n      false\n    );\n    this.virtualScroll = new VirtualScroll(eventsTarget, {\n      touchMultiplier,\n      wheelMultiplier\n    });\n    this.virtualScroll.on(\"scroll\", this.onVirtualScroll);\n    if (this.options.autoToggle) {\n      this.rootElement.addEventListener(\"transitionend\", this.onTransitionEnd, {\n        passive: true\n      });\n    }\n    if (this.options.autoRaf) {\n      this.__rafID = requestAnimationFrame(this.raf);\n    }\n  }\n  /**\n   * Destroy the lenis instance, remove all event listeners and clean up the class name\n   */\n  destroy() {\n    this.emitter.destroy();\n    this.options.wrapper.removeEventListener(\n      \"scroll\",\n      this.onNativeScroll,\n      false\n    );\n    this.options.wrapper.removeEventListener(\"scrollend\", this.onScrollEnd, {\n      capture: true\n    });\n    this.options.wrapper.removeEventListener(\n      \"pointerdown\",\n      this.onPointerDown,\n      false\n    );\n    if (this.options.anchors && this.options.wrapper === window) {\n      this.options.wrapper.removeEventListener(\n        \"click\",\n        this.onClick,\n        false\n      );\n    }\n    this.virtualScroll.destroy();\n    this.dimensions.destroy();\n    this.cleanUpClassName();\n    if (this.__rafID) {\n      cancelAnimationFrame(this.__rafID);\n    }\n  }\n  on(event, callback) {\n    return this.emitter.on(event, callback);\n  }\n  off(event, callback) {\n    return this.emitter.off(event, callback);\n  }\n  onScrollEnd = (e) => {\n    if (!(e instanceof CustomEvent)) {\n      if (this.isScrolling === \"smooth\" || this.isScrolling === false) {\n        e.stopPropagation();\n      }\n    }\n  };\n  dispatchScrollendEvent = () => {\n    this.options.wrapper.dispatchEvent(\n      new CustomEvent(\"scrollend\", {\n        bubbles: this.options.wrapper === window,\n        // cancelable: false,\n        detail: {\n          lenisScrollEnd: true\n        }\n      })\n    );\n  };\n  onTransitionEnd = (event) => {\n    if (event.propertyName.includes(\"overflow\")) {\n      const property = this.isHorizontal ? \"overflow-x\" : \"overflow-y\";\n      const overflow = getComputedStyle(this.rootElement)[property];\n      if ([\"hidden\", \"clip\"].includes(overflow)) {\n        this.internalStop();\n      } else {\n        this.internalStart();\n      }\n    }\n  };\n  setScroll(scroll) {\n    if (this.isHorizontal) {\n      this.options.wrapper.scrollTo({ left: scroll, behavior: \"instant\" });\n    } else {\n      this.options.wrapper.scrollTo({ top: scroll, behavior: \"instant\" });\n    }\n  }\n  onClick = (event) => {\n    const path = event.composedPath();\n    const anchor = path.find(\n      (node) => node instanceof HTMLAnchorElement && (node.getAttribute(\"href\")?.startsWith(\"#\") || node.getAttribute(\"href\")?.startsWith(\"/#\") || node.getAttribute(\"href\")?.startsWith(\"./#\"))\n    );\n    if (anchor) {\n      const id = anchor.getAttribute(\"href\");\n      if (id) {\n        const options = typeof this.options.anchors === \"object\" && this.options.anchors ? this.options.anchors : void 0;\n        let target = `#${id.split(\"#\")[1]}`;\n        if ([\"#\", \"/#\", \"./#\", \"#top\", \"/#top\", \"./#top\"].includes(id)) {\n          target = 0;\n        }\n        this.scrollTo(target, options);\n      }\n    }\n  };\n  onPointerDown = (event) => {\n    if (event.button === 1) {\n      this.reset();\n    }\n  };\n  onVirtualScroll = (data) => {\n    if (typeof this.options.virtualScroll === \"function\" && this.options.virtualScroll(data) === false)\n      return;\n    const { deltaX, deltaY, event } = data;\n    this.emitter.emit(\"virtual-scroll\", { deltaX, deltaY, event });\n    if (event.ctrlKey) return;\n    if (event.lenisStopPropagation) return;\n    const isTouch = event.type.includes(\"touch\");\n    const isWheel = event.type.includes(\"wheel\");\n    this.isTouching = event.type === \"touchstart\" || event.type === \"touchmove\";\n    const isClickOrTap = deltaX === 0 && deltaY === 0;\n    const isTapToStop = this.options.syncTouch && isTouch && event.type === \"touchstart\" && isClickOrTap && !this.isStopped && !this.isLocked;\n    if (isTapToStop) {\n      this.reset();\n      return;\n    }\n    const isUnknownGesture = this.options.gestureOrientation === \"vertical\" && deltaY === 0 || this.options.gestureOrientation === \"horizontal\" && deltaX === 0;\n    if (isClickOrTap || isUnknownGesture) {\n      return;\n    }\n    let composedPath = event.composedPath();\n    composedPath = composedPath.slice(0, composedPath.indexOf(this.rootElement));\n    const prevent = this.options.prevent;\n    if (!!composedPath.find(\n      (node) => node instanceof HTMLElement && (typeof prevent === \"function\" && prevent?.(node) || node.hasAttribute?.(\"data-lenis-prevent\") || isTouch && node.hasAttribute?.(\"data-lenis-prevent-touch\") || isWheel && node.hasAttribute?.(\"data-lenis-prevent-wheel\") || this.options.allowNestedScroll && this.checkNestedScroll(node, { deltaX, deltaY }))\n    ))\n      return;\n    if (this.isStopped || this.isLocked) {\n      if (event.cancelable) {\n        event.preventDefault();\n      }\n      return;\n    }\n    const isSmooth = this.options.syncTouch && isTouch || this.options.smoothWheel && isWheel;\n    if (!isSmooth) {\n      this.isScrolling = \"native\";\n      this.animate.stop();\n      event.lenisStopPropagation = true;\n      return;\n    }\n    let delta = deltaY;\n    if (this.options.gestureOrientation === \"both\") {\n      delta = Math.abs(deltaY) > Math.abs(deltaX) ? deltaY : deltaX;\n    } else if (this.options.gestureOrientation === \"horizontal\") {\n      delta = deltaX;\n    }\n    if (!this.options.overscroll || this.options.infinite || this.options.wrapper !== window && (this.animatedScroll > 0 && this.animatedScroll < this.limit || this.animatedScroll === 0 && deltaY > 0 || this.animatedScroll === this.limit && deltaY < 0)) {\n      event.lenisStopPropagation = true;\n    }\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n    const isSyncTouch = isTouch && this.options.syncTouch;\n    const isTouchEnd = isTouch && event.type === \"touchend\";\n    const hasTouchInertia = isTouchEnd;\n    if (hasTouchInertia) {\n      delta = Math.sign(this.velocity) * Math.pow(Math.abs(this.velocity), this.options.touchInertiaExponent);\n    }\n    this.scrollTo(this.targetScroll + delta, {\n      programmatic: false,\n      ...isSyncTouch ? {\n        lerp: hasTouchInertia ? this.options.syncTouchLerp : 1\n        // immediate: !hasTouchInertia,\n      } : {\n        lerp: this.options.lerp,\n        duration: this.options.duration,\n        easing: this.options.easing\n      }\n    });\n  };\n  /**\n   * Force lenis to recalculate the dimensions\n   */\n  resize() {\n    this.dimensions.resize();\n    this.animatedScroll = this.targetScroll = this.actualScroll;\n    this.emit();\n  }\n  emit() {\n    this.emitter.emit(\"scroll\", this);\n  }\n  onNativeScroll = () => {\n    if (this._resetVelocityTimeout !== null) {\n      clearTimeout(this._resetVelocityTimeout);\n      this._resetVelocityTimeout = null;\n    }\n    if (this._preventNextNativeScrollEvent) {\n      this._preventNextNativeScrollEvent = false;\n      return;\n    }\n    if (this.isScrolling === false || this.isScrolling === \"native\") {\n      const lastScroll = this.animatedScroll;\n      this.animatedScroll = this.targetScroll = this.actualScroll;\n      this.lastVelocity = this.velocity;\n      this.velocity = this.animatedScroll - lastScroll;\n      this.direction = Math.sign(\n        this.animatedScroll - lastScroll\n      );\n      if (!this.isStopped) {\n        this.isScrolling = \"native\";\n      }\n      this.emit();\n      if (this.velocity !== 0) {\n        this._resetVelocityTimeout = setTimeout(() => {\n          this.lastVelocity = this.velocity;\n          this.velocity = 0;\n          this.isScrolling = false;\n          this.emit();\n        }, 400);\n      }\n    }\n  };\n  reset() {\n    this.isLocked = false;\n    this.isScrolling = false;\n    this.animatedScroll = this.targetScroll = this.actualScroll;\n    this.lastVelocity = this.velocity = 0;\n    this.animate.stop();\n  }\n  /**\n   * Start lenis scroll after it has been stopped\n   */\n  start() {\n    if (!this.isStopped) return;\n    if (this.options.autoToggle) {\n      this.rootElement.style.removeProperty(\"overflow\");\n      return;\n    }\n    this.internalStart();\n  }\n  internalStart() {\n    if (!this.isStopped) return;\n    this.reset();\n    this.isStopped = false;\n    this.emit();\n  }\n  /**\n   * Stop lenis scroll\n   */\n  stop() {\n    if (this.isStopped) return;\n    if (this.options.autoToggle) {\n      this.rootElement.style.setProperty(\"overflow\", \"clip\");\n      return;\n    }\n    this.internalStop();\n  }\n  internalStop() {\n    if (this.isStopped) return;\n    this.reset();\n    this.isStopped = true;\n    this.emit();\n  }\n  /**\n   * RequestAnimationFrame for lenis\n   *\n   * @param time The time in ms from an external clock like `requestAnimationFrame` or Tempus\n   */\n  raf = (time) => {\n    const deltaTime = time - (this.time || time);\n    this.time = time;\n    this.animate.advance(deltaTime * 1e-3);\n    if (this.options.autoRaf) {\n      this.__rafID = requestAnimationFrame(this.raf);\n    }\n  };\n  /**\n   * Scroll to a target value\n   *\n   * @param target The target value to scroll to\n   * @param options The options for the scroll\n   *\n   * @example\n   * lenis.scrollTo(100, {\n   *   offset: 100,\n   *   duration: 1,\n   *   easing: (t) => 1 - Math.cos((t * Math.PI) / 2),\n   *   lerp: 0.1,\n   *   onStart: () => {\n   *     console.log('onStart')\n   *   },\n   *   onComplete: () => {\n   *     console.log('onComplete')\n   *   },\n   * })\n   */\n  scrollTo(target, {\n    offset = 0,\n    immediate = false,\n    lock = false,\n    duration = this.options.duration,\n    easing = this.options.easing,\n    lerp: lerp2 = this.options.lerp,\n    onStart,\n    onComplete,\n    force = false,\n    // scroll even if stopped\n    programmatic = true,\n    // called from outside of the class\n    userData\n  } = {}) {\n    if ((this.isStopped || this.isLocked) && !force) return;\n    if (typeof target === \"string\" && [\"top\", \"left\", \"start\"].includes(target)) {\n      target = 0;\n    } else if (typeof target === \"string\" && [\"bottom\", \"right\", \"end\"].includes(target)) {\n      target = this.limit;\n    } else {\n      let node;\n      if (typeof target === \"string\") {\n        node = document.querySelector(target);\n      } else if (target instanceof HTMLElement && target?.nodeType) {\n        node = target;\n      }\n      if (node) {\n        if (this.options.wrapper !== window) {\n          const wrapperRect = this.rootElement.getBoundingClientRect();\n          offset -= this.isHorizontal ? wrapperRect.left : wrapperRect.top;\n        }\n        const rect = node.getBoundingClientRect();\n        target = (this.isHorizontal ? rect.left : rect.top) + this.animatedScroll;\n      }\n    }\n    if (typeof target !== \"number\") return;\n    target += offset;\n    target = Math.round(target);\n    if (this.options.infinite) {\n      if (programmatic) {\n        this.targetScroll = this.animatedScroll = this.scroll;\n        const distance = target - this.animatedScroll;\n        if (distance > this.limit / 2) {\n          target = target - this.limit;\n        } else if (distance < -this.limit / 2) {\n          target = target + this.limit;\n        }\n      }\n    } else {\n      target = clamp(0, target, this.limit);\n    }\n    if (target === this.targetScroll) {\n      onStart?.(this);\n      onComplete?.(this);\n      return;\n    }\n    this.userData = userData ?? {};\n    if (immediate) {\n      this.animatedScroll = this.targetScroll = target;\n      this.setScroll(this.scroll);\n      this.reset();\n      this.preventNextNativeScrollEvent();\n      this.emit();\n      onComplete?.(this);\n      this.userData = {};\n      requestAnimationFrame(() => {\n        this.dispatchScrollendEvent();\n      });\n      return;\n    }\n    if (!programmatic) {\n      this.targetScroll = target;\n    }\n    if (typeof duration === \"number\" && typeof easing !== \"function\") {\n      easing = defaultEasing;\n    } else if (typeof easing === \"function\" && typeof duration !== \"number\") {\n      duration = 1;\n    }\n    this.animate.fromTo(this.animatedScroll, target, {\n      duration,\n      easing,\n      lerp: lerp2,\n      onStart: () => {\n        if (lock) this.isLocked = true;\n        this.isScrolling = \"smooth\";\n        onStart?.(this);\n      },\n      onUpdate: (value, completed) => {\n        this.isScrolling = \"smooth\";\n        this.lastVelocity = this.velocity;\n        this.velocity = value - this.animatedScroll;\n        this.direction = Math.sign(this.velocity);\n        this.animatedScroll = value;\n        this.setScroll(this.scroll);\n        if (programmatic) {\n          this.targetScroll = value;\n        }\n        if (!completed) this.emit();\n        if (completed) {\n          this.reset();\n          this.emit();\n          onComplete?.(this);\n          this.userData = {};\n          requestAnimationFrame(() => {\n            this.dispatchScrollendEvent();\n          });\n          this.preventNextNativeScrollEvent();\n        }\n      }\n    });\n  }\n  preventNextNativeScrollEvent() {\n    this._preventNextNativeScrollEvent = true;\n    requestAnimationFrame(() => {\n      this._preventNextNativeScrollEvent = false;\n    });\n  }\n  checkNestedScroll(node, { deltaX, deltaY }) {\n    const time = Date.now();\n    const cache = node._lenis ??= {};\n    let hasOverflowX, hasOverflowY, isScrollableX, isScrollableY, scrollWidth, scrollHeight, clientWidth, clientHeight;\n    const gestureOrientation = this.options.gestureOrientation;\n    if (time - (cache.time ?? 0) > 2e3) {\n      cache.time = Date.now();\n      const computedStyle = window.getComputedStyle(node);\n      cache.computedStyle = computedStyle;\n      const overflowXString = computedStyle.overflowX;\n      const overflowYString = computedStyle.overflowY;\n      hasOverflowX = [\"auto\", \"overlay\", \"scroll\"].includes(overflowXString);\n      hasOverflowY = [\"auto\", \"overlay\", \"scroll\"].includes(overflowYString);\n      cache.hasOverflowX = hasOverflowX;\n      cache.hasOverflowY = hasOverflowY;\n      if (!hasOverflowX && !hasOverflowY) return false;\n      if (gestureOrientation === \"vertical\" && !hasOverflowY) return false;\n      if (gestureOrientation === \"horizontal\" && !hasOverflowX) return false;\n      scrollWidth = node.scrollWidth;\n      scrollHeight = node.scrollHeight;\n      clientWidth = node.clientWidth;\n      clientHeight = node.clientHeight;\n      isScrollableX = scrollWidth > clientWidth;\n      isScrollableY = scrollHeight > clientHeight;\n      cache.isScrollableX = isScrollableX;\n      cache.isScrollableY = isScrollableY;\n      cache.scrollWidth = scrollWidth;\n      cache.scrollHeight = scrollHeight;\n      cache.clientWidth = clientWidth;\n      cache.clientHeight = clientHeight;\n    } else {\n      isScrollableX = cache.isScrollableX;\n      isScrollableY = cache.isScrollableY;\n      hasOverflowX = cache.hasOverflowX;\n      hasOverflowY = cache.hasOverflowY;\n      scrollWidth = cache.scrollWidth;\n      scrollHeight = cache.scrollHeight;\n      clientWidth = cache.clientWidth;\n      clientHeight = cache.clientHeight;\n    }\n    if (!hasOverflowX && !hasOverflowY || !isScrollableX && !isScrollableY) {\n      return false;\n    }\n    if (gestureOrientation === \"vertical\" && (!hasOverflowY || !isScrollableY))\n      return false;\n    if (gestureOrientation === \"horizontal\" && (!hasOverflowX || !isScrollableX))\n      return false;\n    let orientation;\n    if (gestureOrientation === \"horizontal\") {\n      orientation = \"x\";\n    } else if (gestureOrientation === \"vertical\") {\n      orientation = \"y\";\n    } else {\n      const isScrollingX = deltaX !== 0;\n      const isScrollingY = deltaY !== 0;\n      if (isScrollingX && hasOverflowX && isScrollableX) {\n        orientation = \"x\";\n      }\n      if (isScrollingY && hasOverflowY && isScrollableY) {\n        orientation = \"y\";\n      }\n    }\n    if (!orientation) return false;\n    let scroll, maxScroll, delta, hasOverflow, isScrollable;\n    if (orientation === \"x\") {\n      scroll = node.scrollLeft;\n      maxScroll = scrollWidth - clientWidth;\n      delta = deltaX;\n      hasOverflow = hasOverflowX;\n      isScrollable = isScrollableX;\n    } else if (orientation === \"y\") {\n      scroll = node.scrollTop;\n      maxScroll = scrollHeight - clientHeight;\n      delta = deltaY;\n      hasOverflow = hasOverflowY;\n      isScrollable = isScrollableY;\n    } else {\n      return false;\n    }\n    const willScroll = delta > 0 ? scroll < maxScroll : scroll > 0;\n    return willScroll && hasOverflow && isScrollable;\n  }\n  /**\n   * The root element on which lenis is instanced\n   */\n  get rootElement() {\n    return this.options.wrapper === window ? document.documentElement : this.options.wrapper;\n  }\n  /**\n   * The limit which is the maximum scroll value\n   */\n  get limit() {\n    if (this.options.__experimental__naiveDimensions) {\n      if (this.isHorizontal) {\n        return this.rootElement.scrollWidth - this.rootElement.clientWidth;\n      } else {\n        return this.rootElement.scrollHeight - this.rootElement.clientHeight;\n      }\n    } else {\n      return this.dimensions.limit[this.isHorizontal ? \"x\" : \"y\"];\n    }\n  }\n  /**\n   * Whether or not the scroll is horizontal\n   */\n  get isHorizontal() {\n    return this.options.orientation === \"horizontal\";\n  }\n  /**\n   * The actual scroll value\n   */\n  get actualScroll() {\n    const wrapper = this.options.wrapper;\n    return this.isHorizontal ? wrapper.scrollX ?? wrapper.scrollLeft : wrapper.scrollY ?? wrapper.scrollTop;\n  }\n  /**\n   * The current scroll value\n   */\n  get scroll() {\n    return this.options.infinite ? modulo(this.animatedScroll, this.limit) : this.animatedScroll;\n  }\n  /**\n   * The progress of the scroll relative to the limit\n   */\n  get progress() {\n    return this.limit === 0 ? 1 : this.scroll / this.limit;\n  }\n  /**\n   * Current scroll state\n   */\n  get isScrolling() {\n    return this._isScrolling;\n  }\n  set isScrolling(value) {\n    if (this._isScrolling !== value) {\n      this._isScrolling = value;\n      this.updateClassName();\n    }\n  }\n  /**\n   * Check if lenis is stopped\n   */\n  get isStopped() {\n    return this._isStopped;\n  }\n  set isStopped(value) {\n    if (this._isStopped !== value) {\n      this._isStopped = value;\n      this.updateClassName();\n    }\n  }\n  /**\n   * Check if lenis is locked\n   */\n  get isLocked() {\n    return this._isLocked;\n  }\n  set isLocked(value) {\n    if (this._isLocked !== value) {\n      this._isLocked = value;\n      this.updateClassName();\n    }\n  }\n  /**\n   * Check if lenis is smooth scrolling\n   */\n  get isSmooth() {\n    return this.isScrolling === \"smooth\";\n  }\n  /**\n   * The class name applied to the wrapper element\n   */\n  get className() {\n    let className = \"lenis\";\n    if (this.options.autoToggle) className += \" lenis-autoToggle\";\n    if (this.isStopped) className += \" lenis-stopped\";\n    if (this.isLocked) className += \" lenis-locked\";\n    if (this.isScrolling) className += \" lenis-scrolling\";\n    if (this.isScrolling === \"smooth\") className += \" lenis-smooth\";\n    return className;\n  }\n  updateClassName() {\n    this.cleanUpClassName();\n    this.rootElement.className = `${this.rootElement.className} ${this.className}`.trim();\n  }\n  cleanUpClassName() {\n    this.rootElement.className = this.rootElement.className.replace(/lenis(-\\w+)?/g, \"\").trim();\n  }\n};\n\n//# sourceMappingURL=lenis.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lenis/dist/lenis.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharrnish%2FDesktop%2Fcodegrid-madeinuxstudio-page-transition-nextjs%2Fnode_modules%2Flenis%2Fdist%2Flenis-react.mjs%22%2C%22ids%22%3A%5B%22ReactLenis%22%5D%7D&server=false!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharrnish%2FDesktop%2Fcodegrid-madeinuxstudio-page-transition-nextjs%2Fnode_modules%2Flenis%2Fdist%2Flenis-react.mjs%22%2C%22ids%22%3A%5B%22ReactLenis%22%5D%7D&server=false! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/lenis/dist/lenis-react.mjs */ \"(app-pages-browser)/./node_modules/lenis/dist/lenis-react.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZoYXJybmlzaCUyRkRlc2t0b3AlMkZjb2RlZ3JpZC1tYWRlaW51eHN0dWRpby1wYWdlLXRyYW5zaXRpb24tbmV4dGpzJTJGbm9kZV9tb2R1bGVzJTJGbGVuaXMlMkZkaXN0JTJGbGVuaXMtcmVhY3QubWpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyUmVhY3RMZW5pcyUyMiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDRNQUFtTCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUmVhY3RMZW5pc1wiXSAqLyBcIi9Vc2Vycy9oYXJybmlzaC9EZXNrdG9wL2NvZGVncmlkLW1hZGVpbnV4c3R1ZGlvLXBhZ2UtdHJhbnNpdGlvbi1uZXh0anMvbm9kZV9tb2R1bGVzL2xlbmlzL2Rpc3QvbGVuaXMtcmVhY3QubWpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharrnish%2FDesktop%2Fcodegrid-madeinuxstudio-page-transition-nextjs%2Fnode_modules%2Flenis%2Fdist%2Flenis-react.mjs%22%2C%22ids%22%3A%5B%22ReactLenis%22%5D%7D&server=false!\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharrnish%2FDesktop%2Fcodegrid-madeinuxstudio-page-transition-nextjs%2Fnode_modules%2Flenis%2Fdist%2Flenis-react.mjs%22%2C%22ids%22%3A%5B%22ReactLenis%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);