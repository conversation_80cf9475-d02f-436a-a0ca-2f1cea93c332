'use client';
import { useState, useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import styles from './style.module.scss';

export default function MenuImages({ className = "", menuImages = [], hoveredIndex = null }) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showImage, setShowImage] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [autoMode, setAutoMode] = useState(true);
  const currentImageRef = useRef(null);
  const nextImageRef = useRef(null);
  const containerRef = useRef(null);
  const rotationTween = useRef(null);

  // Images par défaut si aucune n'est fournie
  const defaultImages = [
    '/images/lucas-joliveau-siege-ordinateur-portable.png',
    '/images/lucas-joliveau-siege-ordinateur-portable.png',
    '/images/lucas-joliveau-siege-ordinateur-portable.png',
    '/images/lucas-joliveau-siege-ordinateur-portable.png'
  ];

  const images = menuImages.length > 0 ? menuImages : defaultImages;

  useEffect(() => {
    // Démarrer avec la première image
    setCurrentImageIndex(0);
    setShowImage(true);
    console.log('Image de départ:', images[0]);

    // Démarrer l'animation de rotation continue
    startRotationAnimation();
  }, [images]);

  const startRotationAnimation = () => {
    if (currentImageRef.current) {
      // Arrêter l'animation précédente si elle existe
      if (rotationTween.current) {
        rotationTween.current.kill();
      }

      // Nouvelle animation de rotation continue
      rotationTween.current = gsap.to(currentImageRef.current, {
        rotation: 360,
        duration: 20,
        ease: "none",
        repeat: -1
      });
    }
  };

  // Gérer le survol avec transition smooth
  useEffect(() => {
    if (hoveredIndex !== null && hoveredIndex !== currentImageIndex && !isTransitioning) {
      setAutoMode(false);
      performSmoothTransition(hoveredIndex);
    } else if (hoveredIndex === null && !autoMode) {
      setAutoMode(true);
    }
  }, [hoveredIndex]);

  // Cycle automatique (seulement en mode auto)
  useEffect(() => {
    if (!autoMode) return;

    const interval = setInterval(() => {
      if (!isTransitioning && images.length > 1 && autoMode) {
        const nextIndex = (currentImageIndex + 1) % images.length;
        performSmoothTransition(nextIndex);
      }
    }, 4000);

    return () => clearInterval(interval);
  }, [currentImageIndex, isTransitioning, images.length, autoMode]);

  const performSmoothTransition = (targetIndex) => {
    if (targetIndex === currentImageIndex || isTransitioning) return;

    setIsTransitioning(true);
    console.log(`Transition smooth: ${currentImageIndex} → ${targetIndex}`);

    // Préparer la nouvelle image
    if (nextImageRef.current) {
      gsap.set(nextImageRef.current, {
        opacity: 0,
        rotation: 0,
        scale: 1.1 // Légèrement plus grande pour l'effet
      });
    }

    // Timeline pour une transition fluide
    const tl = gsap.timeline({
      onComplete: () => {
        setCurrentImageIndex(targetIndex);
        setIsTransitioning(false);
        startRotationAnimation(); // Redémarrer la rotation
      }
    });

    // Phase 1: Fade out + rotation de l'image actuelle
    tl.to(currentImageRef.current, {
      opacity: 0,
      rotation: "+=15", // Petite rotation supplémentaire
      scale: 0.95,
      duration: 0.4,
      ease: "power2.inOut"
    })

    // Phase 2: Fade in + scale de la nouvelle image
    .to(nextImageRef.current, {
      opacity: 1,
      scale: 1,
      duration: 0.5,
      ease: "power2.out"
    }, "-=0.2") // Overlap pour plus de fluidité

    // Phase 3: Reset de l'image actuelle pour le prochain cycle
    .set(currentImageRef.current, {
      opacity: 1,
      rotation: 0,
      scale: 1
    });
  };



  return (
    <div ref={containerRef} className={`${styles.container} ${className}`}>
      {/* Image actuelle */}
      <img
        ref={currentImageRef}
        src={images[currentImageIndex]}
        alt=""
        className={`${styles.image} ${showImage ? styles.visible : ''}`}
        key={`current-${currentImageIndex}`}
      />

      {/* Image suivante (pour les transitions) */}
      <img
        ref={nextImageRef}
        src={hoveredIndex !== null ? images[hoveredIndex] : images[(currentImageIndex + 1) % images.length]}
        alt=""
        className={`${styles.image} ${styles.nextImage}`}
        key={`next-${hoveredIndex !== null ? hoveredIndex : (currentImageIndex + 1) % images.length}`}
      />
    </div>
  );
}
