'use client';
import { useState, useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import styles from './style.module.scss';

export default function MenuImages({ className = "", menuImages = [] }) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [nextImageIndex, setNextImageIndex] = useState(1);
  const [showImage, setShowImage] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const currentImageRef = useRef(null);
  const nextImageRef = useRef(null);
  const containerRef = useRef(null);

  // Images par défaut si aucune n'est fournie
  const defaultImages = [
    '/images/lucas-joliveau-siege-ordinateur-portable.png',
    '/images/lucas-joliveau-siege-ordinateur-portable.png',
    '/images/lucas-joliveau-siege-ordinateur-portable.png',
    '/images/lucas-joliveau-siege-ordinateur-portable.png'
  ];

  const images = menuImages.length > 0 ? menuImages : defaultImages;

  useEffect(() => {
    // Démarrer avec la première image
    setCurrentImageIndex(0);
    setNextImageIndex(1 % images.length);
    setShowImage(true);
    console.log('Image de départ:', images[0]);
    
    // Démarrer l'animation de rotation
    if (currentImageRef.current) {
      gsap.to(currentImageRef.current, {
        rotation: 360,
        duration: 20,
        ease: "none",
        repeat: -1
      });
    }
  }, [images]);

  // Animation de rotation pour la nouvelle image
  useEffect(() => {
    if (currentImageRef.current && showImage) {
      gsap.set(currentImageRef.current, { rotation: 0 });
      gsap.to(currentImageRef.current, {
        rotation: 360,
        duration: 20,
        ease: "none",
        repeat: -1
      });
    }
  }, [currentImageIndex, showImage]);

  // Démarrer le cycle automatique des images
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isTransitioning && images.length > 1) {
        handleImageTransition();
      }
    }, 4000); // Change d'image toutes les 4 secondes

    return () => clearInterval(interval);
  }, [currentImageIndex, isTransitioning, images.length]);

  const handleImageTransition = () => {
    if (isTransitioning || images.length <= 1) return;
    
    setIsTransitioning(true);
    const nextIndex = (currentImageIndex + 1) % images.length;
    console.log(`Transition image: ${currentImageIndex} → ${nextIndex}`);
    
    // Fade out current, fade in next
    gsap.to(currentImageRef.current, {
      opacity: 0,
      duration: 0.6,
      ease: "power2.inOut"
    });
    
    gsap.to(nextImageRef.current, {
      opacity: 1,
      duration: 0.6,
      ease: "power2.inOut",
      onComplete: () => {
        // Swap les images
        setCurrentImageIndex(nextIndex);
        setNextImageIndex((nextIndex + 1) % images.length);
        
        // Reset opacities
        gsap.set(currentImageRef.current, { opacity: 1 });
        gsap.set(nextImageRef.current, { opacity: 0 });
        
        setIsTransitioning(false);
      }
    });
  };

  return (
    <div ref={containerRef} className={`${styles.container} ${className}`}>
      {/* Image actuelle */}
      <img
        ref={currentImageRef}
        src={images[currentImageIndex]}
        alt=""
        className={`${styles.image} ${showImage ? styles.visible : ''}`}
        key={`current-${currentImageIndex}`}
      />

      {/* Image suivante (préchargée, invisible) */}
      <img
        ref={nextImageRef}
        src={images[nextImageIndex]}
        alt=""
        className={`${styles.image} ${styles.nextImage}`}
        key={`next-${nextImageIndex}`}
      />
    </div>
  );
}
