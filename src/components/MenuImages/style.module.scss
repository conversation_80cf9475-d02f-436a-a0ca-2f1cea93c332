.container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  z-index: 2;
  will-change: transform, opacity, scale;
  transform-origin: center center;

  &.visible {
    opacity: 1;
  }
}

.nextImage {
  opacity: 0;
  z-index: 3;
  transform-origin: center center;
}
