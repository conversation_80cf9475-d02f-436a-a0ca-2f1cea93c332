'use client';
import { useState, useRef, useEffect, forwardRef } from 'react';
import styles from './style.module.scss';

const VideoWithFallback = forwardRef(({
  videoSrc,
  fallbackImage,
  alt = "",
  className = "",
  onVideoEnd,
  ...props
}, ref) => {
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [showVideo, setShowVideo] = useState(false);
  const videoRef = useRef(null);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    // Réinitialiser les états quand la source change
    setIsVideoLoaded(false);
    setShowVideo(false);

    const handleCanPlayThrough = () => {
      setIsVideoLoaded(true);
      // Petit délai pour une transition plus fluide
      setTimeout(() => {
        setShowVideo(true);
      }, 100);
    };

    const handleLoadedData = () => {
      // Vidéo prête à être jouée
      setIsVideoLoaded(true);
    };

    const handleVideoEnd = () => {
      if (onVideoEnd) {
        onVideoEnd();
      }
    };

    video.addEventListener('canplaythrough', handleCanPlayThrough);
    video.addEventListener('loadeddata', handleLoadedData);
    video.addEventListener('ended', handleVideoEnd);

    // Précharger la vidéo
    video.load();

    return () => {
      video.removeEventListener('canplaythrough', handleCanPlayThrough);
      video.removeEventListener('loadeddata', handleLoadedData);
      video.removeEventListener('ended', handleVideoEnd);
    };
  }, [videoSrc, onVideoEnd]);

  return (
    <div className={`${styles.videoContainer} ${className}`} {...props}>
      {/* Image de backup - toujours présente */}
      <img 
        src={fallbackImage} 
        alt={alt}
        className={`${styles.fallbackImage} ${showVideo ? styles.hidden : ''}`}
      />
      
      {/* Vidéo - s'affiche quand elle est chargée */}
      <video
        ref={videoRef}
        className={`${styles.video} ${showVideo ? styles.visible : ''}`}
        autoPlay
        muted
        playsInline
        preload="auto"
      >
        <source src={videoSrc} type="video/webm" />
        {/* Fallback pour les navigateurs qui ne supportent pas WebM */}
        Your browser does not support the video tag.
      </video>
    </div>
  );
});

VideoWithFallback.displayName = "VideoWithFallback";

export default VideoWithFallback;
