.container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.fallbackImage,
.video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease-in-out;
}

.fallbackImage {
  opacity: 1;
  z-index: 1;
  
  &.hidden {
    opacity: 0;
  }
}

.video {
  opacity: 0;
  z-index: 2;

  &.visible {
    opacity: 1;
  }
}

.nextVideo {
  opacity: 0;
  z-index: 3;
}
