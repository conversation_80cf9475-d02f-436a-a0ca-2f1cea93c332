'use client';
import { useState, useEffect } from 'react';
import VideoWithFallback from '@/components/VideoWithFallback';

export default function MenuVideo({ className = "" }) {
  const [selectedVideo, setSelectedVideo] = useState(null);

  // Liste des vidéos disponibles en 1080p
  const videos = [
    '/videos/copilote-cards-1080.webm',
    '/videos/kolabus-grid-1080.webm',
    '/videos/numigi-grid-1080.webm'
  ];

  useEffect(() => {
    // Sélectionner une vidéo aléatoirement au montage du composant
    const randomIndex = Math.floor(Math.random() * videos.length);
    setSelectedVideo(videos[randomIndex]);
  }, []);

  // Ne pas rendre le composant tant qu'une vidéo n'est pas sélectionnée
  if (!selectedVideo) {
    return (
      <img 
        src="/images/lucas-joliveau-siege-ordinateur-portable.png" 
        alt="" 
        className={className}
      />
    );
  }

  return (
    <VideoWithFallback
      videoSrc={selectedVideo}
      fallbackImage="/images/lucas-joliveau-siege-ordinateur-portable.png"
      alt=""
      className={className}
    />
  );
}
