'use client';
import { useState, useEffect, useRef } from 'react';
import styles from './style.module.scss';

export default function MenuVideo({ className = "" }) {
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [showVideo, setShowVideo] = useState(false);
  const videoRef = useRef(null);

  // Liste des vidéos disponibles en 1080p
  const videos = [
    '/videos/copilote-cards-1080.webm',
    '/videos/kolabus-grid-1080.webm',
    '/videos/numigi-grid-1080.webm'
  ];

  useEffect(() => {
    // Démarrer avec une vidéo aléatoire
    const randomIndex = Math.floor(Math.random() * videos.length);
    setCurrentVideoIndex(randomIndex);
    console.log('Vidéo de départ:', videos[randomIndex]);
  }, []);

  const handleVideoEnd = () => {
    const nextIndex = (currentVideoIndex + 1) % videos.length;
    console.log(`Vidéo ${currentVideoIndex} terminée, passage à ${nextIndex}`);
    setCurrentVideoIndex(nextIndex);
  };

  const handleVideoLoaded = () => {
    setShowVideo(true);
  };

  return (
    <div className={`${styles.container} ${className}`}>
      {/* Image de backup */}
      <img
        src="/images/lucas-joliveau-siege-ordinateur-portable.png"
        alt=""
        className={`${styles.fallbackImage} ${showVideo ? styles.hidden : ''}`}
      />

      {/* Vidéo */}
      <video
        ref={videoRef}
        className={`${styles.video} ${showVideo ? styles.visible : ''}`}
        src={videos[currentVideoIndex]}
        autoPlay
        muted
        playsInline
        preload="auto"
        onLoadedData={handleVideoLoaded}
        onEnded={handleVideoEnd}
        key={currentVideoIndex}
      />
    </div>
  );
}
