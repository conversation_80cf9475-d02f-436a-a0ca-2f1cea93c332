'use client';
import { useState, useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import styles from './style.module.scss';

export default function MenuVideo({ className = "" }) {
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [nextVideoIndex, setNextVideoIndex] = useState(1);
  const [showVideo, setShowVideo] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const currentVideoRef = useRef(null);
  const nextVideoRef = useRef(null);

  // Liste des vidéos disponibles en 1080p
  const videos = [
    '/videos/copilote-cards-1080.webm',
    '/videos/kolabus-grid-1080.webm',
    '/videos/numigi-grid-1080.webm'
  ];

  useEffect(() => {
    // Démarrer avec une vidéo aléatoire
    const randomIndex = Math.floor(Math.random() * videos.length);
    setCurrentVideoIndex(randomIndex);
    setNextVideoIndex((randomIndex + 1) % videos.length);
    console.log('Vidéo de départ:', videos[randomIndex]);
  }, []);

  // Précharger la vidéo suivante
  useEffect(() => {
    if (nextVideoRef.current) {
      nextVideoRef.current.load();
    }
  }, [nextVideoIndex]);

  const handleVideoEnd = () => {
    if (isTransitioning) return;

    setIsTransitioning(true);
    console.log(`Transition: ${currentVideoIndex} → ${nextVideoIndex}`);

    // Démarrer la vidéo suivante
    if (nextVideoRef.current) {
      nextVideoRef.current.currentTime = 0;
      nextVideoRef.current.play();
    }

    // Fade out current, fade in next
    gsap.to(currentVideoRef.current, {
      opacity: 0,
      duration: 0.4,
      ease: "power2.inOut"
    });

    gsap.to(nextVideoRef.current, {
      opacity: 1,
      duration: 0.4,
      ease: "power2.inOut",
      onComplete: () => {
        // Swap les vidéos
        setCurrentVideoIndex(nextVideoIndex);
        setNextVideoIndex((nextVideoIndex + 1) % videos.length);

        // Reset opacities
        gsap.set(currentVideoRef.current, { opacity: 1 });
        gsap.set(nextVideoRef.current, { opacity: 0 });

        setIsTransitioning(false);
      }
    });
  };

  const handleVideoLoaded = () => {
    setShowVideo(true);
  };

  return (
    <div className={`${styles.container} ${className}`}>
      {/* Image de backup */}
      <img
        src="/images/lucas-joliveau-siege-ordinateur-portable.png"
        alt=""
        className={`${styles.fallbackImage} ${showVideo ? styles.hidden : ''}`}
      />

      {/* Vidéo actuelle */}
      <video
        ref={currentVideoRef}
        className={`${styles.video} ${showVideo ? styles.visible : ''}`}
        src={videos[currentVideoIndex]}
        autoPlay
        muted
        playsInline
        preload="auto"
        onLoadedData={handleVideoLoaded}
        onEnded={handleVideoEnd}
        key={`current-${currentVideoIndex}`}
      />

      {/* Vidéo suivante (préchargée, invisible) */}
      <video
        ref={nextVideoRef}
        className={`${styles.video} ${styles.nextVideo}`}
        src={videos[nextVideoIndex]}
        muted
        playsInline
        preload="auto"
        key={`next-${nextVideoIndex}`}
      />
    </div>
  );
}
