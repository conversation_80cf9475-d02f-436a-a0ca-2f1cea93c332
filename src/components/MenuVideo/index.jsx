'use client';
import { useState, useEffect, useRef } from 'react';
import VideoWithFallback from '@/components/VideoWithFallback';

export default function MenuVideo({ className = "" }) {
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [isInitialized, setIsInitialized] = useState(false);
  const videoRef = useRef(null);

  // Liste des vidéos disponibles en 1080p
  const videos = [
    '/videos/copilote-cards-1080.webm',
    '/videos/kolabus-grid-1080.webm',
    '/videos/numigi-grid-1080.webm'
  ];

  useEffect(() => {
    // Démarrer avec une vidéo aléatoire
    const randomIndex = Math.floor(Math.random() * videos.length);
    setCurrentVideoIndex(randomIndex);
    setIsInitialized(true);
  }, []);

  const handleVideoEnd = () => {
    // Passer à la vidéo suivante quand la vidéo actuelle se termine
    console.log('Vidéo terminée, passage à la suivante');
    setCurrentVideoIndex((prevIndex) => {
      const nextIndex = (prevIndex + 1) % videos.length;
      console.log(`Passage de la vidéo ${prevIndex} à la vidéo ${nextIndex}`);
      return nextIndex;
    });
  };

  // Ne pas rendre le composant tant qu'il n'est pas initialisé
  if (!isInitialized) {
    return (
      <img
        src="/images/lucas-joliveau-siege-ordinateur-portable.png"
        alt=""
        className={className}
      />
    );
  }

  return (
    <VideoWithFallback
      ref={videoRef}
      videoSrc={videos[currentVideoIndex]}
      fallbackImage="/images/lucas-joliveau-siege-ordinateur-portable.png"
      alt=""
      className={className}
      onVideoEnd={handleVideoEnd}
      key={currentVideoIndex} // Force le remontage du composant pour chaque nouvelle vidéo
    />
  );
}
